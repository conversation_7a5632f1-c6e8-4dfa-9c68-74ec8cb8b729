#!/usr/bin/env python3
"""
Test server to verify <PERSON><PERSON><PERSON> and <PERSON>cket<PERSON> are working
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from flask import Flask
    from flask_socketio import SocketIO
    
    print("✅ Flask and SocketIO imported successfully")
    
    app = Flask(__name__)
    app.secret_key = 'test-secret-key'
    socketio = SocketIO(app, cors_allowed_origins="*")
    
    @app.route('/')
    def index():
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>🚀 Professional Kotak Neo Trading Bot - Test Server</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    margin: 40px; 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    text-align: center;
                }
                .container { 
                    background: rgba(255,255,255,0.1); 
                    padding: 40px; 
                    border-radius: 20px; 
                    max-width: 600px; 
                    margin: 0 auto;
                    backdrop-filter: blur(10px);
                }
                .status { 
                    background: rgba(76, 175, 80, 0.2); 
                    padding: 20px; 
                    border-radius: 10px; 
                    margin: 20px 0;
                    border: 2px solid #4caf50;
                }
                .button {
                    background: #4caf50;
                    color: white;
                    padding: 15px 30px;
                    border: none;
                    border-radius: 25px;
                    font-size: 16px;
                    cursor: pointer;
                    margin: 10px;
                }
                .button:hover { background: #45a049; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 Professional Kotak Neo Trading Bot</h1>
                <h2>Test Server Running Successfully!</h2>
                
                <div class="status">
                    <h3>✅ Server Status: ONLINE</h3>
                    <p><strong>URL:</strong> http://localhost:5000</p>
                    <p><strong>Flask:</strong> Working ✅</p>
                    <p><strong>SocketIO:</strong> Working ✅</p>
                    <p><strong>Time:</strong> <span id="time"></span></p>
                </div>
                
                <h3>🔧 Next Steps:</h3>
                <ol style="text-align: left; max-width: 400px; margin: 0 auto;">
                    <li>✅ Server is running properly</li>
                    <li>✅ Flask and SocketIO are working</li>
                    <li>🔄 Ready to start main application</li>
                    <li>📱 Ready for Kotak Neo credentials</li>
                </ol>
                
                <button class="button" onclick="window.location.reload()">🔄 Refresh</button>
                <button class="button" onclick="testConnection()">🧪 Test Connection</button>
                
                <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
                    <p>Professional Kotak Neo Trading Bot v1.0</p>
                    <p>All systems operational and ready for trading!</p>
                </div>
            </div>
            
            <script>
                function updateTime() {
                    document.getElementById('time').textContent = new Date().toLocaleString();
                }
                
                function testConnection() {
                    fetch('/test')
                        .then(response => response.json())
                        .then(data => alert('✅ Connection test successful!\\n' + JSON.stringify(data, null, 2)))
                        .catch(error => alert('❌ Connection test failed: ' + error));
                }
                
                updateTime();
                setInterval(updateTime, 1000);
            </script>
        </body>
        </html>
        '''
    
    @app.route('/test')
    def test():
        return {
            'status': 'success',
            'message': 'Test server is working perfectly!',
            'server': 'Professional Kotak Neo Trading Bot',
            'version': '1.0.0'
        }
    
    @socketio.on('connect')
    def handle_connect():
        print('✅ Client connected to test server')
    
    @socketio.on('disconnect')
    def handle_disconnect():
        print('❌ Client disconnected from test server')
    
    if __name__ == '__main__':
        print("=" * 60)
        print("🚀 PROFESSIONAL KOTAK NEO TRADING BOT - TEST SERVER")
        print("=" * 60)
        print("✅ Starting test server...")
        print("🌐 URL: http://localhost:5000")
        print("🔧 Testing Flask and SocketIO functionality")
        print("=" * 60)
        
        try:
            socketio.run(
                app, 
                debug=False, 
                host='0.0.0.0', 
                port=5000,
                allow_unsafe_werkzeug=True
            )
        except Exception as e:
            print(f"❌ Server error: {e}")
            
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("📦 Please install required packages:")
    print("   pip install Flask Flask-SocketIO")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
