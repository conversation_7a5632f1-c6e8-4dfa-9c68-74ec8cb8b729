2025-07-11 17:40:27,374 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-11 17:40:27,375 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-11 17:40:27,466 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-11 17:40:27,466 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-11 17:40:27,466 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-11 17:40:28,957 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-11 17:40:28,957 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-11 17:40:28,971 - werkzeug - WARNING -  * Debugger is active!
2025-07-11 17:40:28,973 - werkzeug - INFO -  * Debugger PIN: 338-597-418
2025-07-11 17:40:39,780 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:40:39] "GET / HTTP/1.1" 200 -
2025-07-11 17:40:39,845 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:40:39] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-11 17:40:40,266 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:40:40] "GET /socket.io/?EIO=4&transport=polling&t=PVvPHC5 HTTP/1.1" 200 -
2025-07-11 17:40:40,599 - __main__ - INFO - Client connected
2025-07-11 17:40:40,599 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:40:40] "POST /socket.io/?EIO=4&transport=polling&t=PVvPHCV&sid=araiem6NPjEwXSfbAAAA HTTP/1.1" 200 -
2025-07-11 17:40:40,599 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:40:40] "GET /socket.io/?EIO=4&transport=polling&t=PVvPHCX&sid=araiem6NPjEwXSfbAAAA HTTP/1.1" 200 -
2025-07-11 17:40:40,666 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:40:40] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-11 17:40:40,852 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:40:40] "GET /socket.io/?EIO=4&transport=polling&t=PVvPHHV&sid=araiem6NPjEwXSfbAAAA HTTP/1.1" 200 -
