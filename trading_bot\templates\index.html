<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Kotak Neo Trading Bot</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>

    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .card-shadow {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-connected { background-color: #10b981; }
        .status-disconnected { background-color: #ef4444; }
        .status-pending { background-color: #f59e0b; }

        .trading-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .trading-card:hover {
            transform: translateY(-2px);
            border-left-color: #667eea;
        }

        .profit { color: #10b981; }
        .loss { color: #ef4444; }

        .chart-container {
            height: 400px;
            background: white;
            border-radius: 8px;
            padding: 16px;
        }

        .modal {
            backdrop-filter: blur(4px);
        }

        .spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: .5; }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            min-width: 300px;
            padding: 16px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success { background-color: #10b981; }
        .notification.error { background-color: #ef4444; }
        .notification.warning { background-color: #f59e0b; }
        .notification.info { background-color: #3b82f6; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navigation Header -->
    <nav class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <i class="fas fa-chart-line text-2xl"></i>
                    <h1 class="text-xl font-bold">Professional Trading Bot</h1>
                    <span class="text-sm opacity-75">Kotak Neo API</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <span class="status-indicator status-disconnected" id="connectionStatus"></span>
                        <span id="connectionText">Disconnected</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm" id="currentTime"></span>
                        <button id="settingsBtn" class="bg-white bg-opacity-20 px-4 py-2 rounded-lg hover:bg-opacity-30 transition">
                            <i class="fas fa-cog"></i> Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Authentication Modal -->
    <div id="authModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4 card-shadow">
            <div class="text-center mb-6">
                <i class="fas fa-shield-alt text-4xl text-blue-500 mb-4"></i>
                <h2 class="text-2xl font-bold">Connect to Kotak Neo</h2>
                <p class="text-gray-600 mt-2">Enter your credentials to start trading</p>
            </div>
            <form id="authForm">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Consumer Key</label>
                        <input type="text" id="consumerKey" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Consumer Secret</label>
                        <input type="password" id="consumerSecret" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mobile Number</label>
                        <input type="tel" id="mobileNumber" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <input type="password" id="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Environment</label>
                        <select id="environment" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="prod">Production</option>
                            <option value="uat">UAT (Testing)</option>
                        </select>
                    </div>
                    <div id="totpSection" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">TOTP (Google Authenticator)</label>
                        <input type="text" id="totp" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" maxlength="6" placeholder="Enter 6-digit TOTP">
                        <p class="text-sm text-gray-600 mt-1">Get this code from your Google Authenticator app</p>
                    </div>
                    <div id="mpinSection" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">MPIN</label>
                        <input type="password" id="mpin" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" maxlength="6" placeholder="Enter 6-digit MPIN">
                        <p class="text-sm text-gray-600 mt-1">Your Kotak Neo trading MPIN</p>
                    </div>
                </div>
                <div class="mt-6">
                    <button type="submit" class="w-full gradient-bg text-white py-2 px-4 rounded-lg hover:opacity-90 transition">
                        <span id="authButtonText">Connect</span>
                        <i id="authSpinner" class="fas fa-spinner spinner ml-2 hidden"></i>
                    </button>
                </div>
                <div class="mt-4 text-center">
                    <p class="text-xs text-gray-500">
                        <i class="fas fa-lock mr-1"></i>
                        Your credentials are securely encrypted and not stored
                    </p>
                </div>
            </form>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div id="dashboardContent" class="hidden">
        <!-- Quick Stats -->
        <div class="container mx-auto px-4 py-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg p-6 card-shadow trading-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">Portfolio Value</p>
                            <p class="text-2xl font-bold" id="portfolioValue">₹0.00</p>
                            <p class="text-xs text-gray-500 mt-1">Total Holdings</p>
                        </div>
                        <i class="fas fa-wallet text-3xl text-blue-500"></i>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 card-shadow trading-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">Today's P&L</p>
                            <p class="text-2xl font-bold" id="todayPnL">₹0.00</p>
                            <p class="text-xs text-gray-500 mt-1">Unrealized</p>
                        </div>
                        <i class="fas fa-chart-line text-3xl text-green-500"></i>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 card-shadow trading-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">Active Positions</p>
                            <p class="text-2xl font-bold" id="activePositions">0</p>
                            <p class="text-xs text-gray-500 mt-1">Open Trades</p>
                        </div>
                        <i class="fas fa-list text-3xl text-purple-500"></i>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 card-shadow trading-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">Active Strategies</p>
                            <p class="text-2xl font-bold" id="activeStrategies">0</p>
                            <p class="text-xs text-gray-500 mt-1">Running</p>
                        </div>
                        <i class="fas fa-robot text-3xl text-orange-500"></i>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Live Chart -->
                <div class="lg:col-span-2 bg-white rounded-lg p-6 card-shadow">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Live Chart</h3>
                        <div class="flex items-center space-x-2">
                            <select id="symbolSelect" class="px-3 py-1 border border-gray-300 rounded text-sm">
                                <option value="">Select Symbol</option>
                                <option value="NIFTY">NIFTY</option>
                                <option value="BANKNIFTY">BANK NIFTY</option>
                                <option value="RELIANCE">RELIANCE</option>
                                <option value="TCS">TCS</option>
                                <option value="INFY">INFOSYS</option>
                                <option value="HDFCBANK">HDFC BANK</option>
                            </select>
                            <select id="exchangeSelect" class="px-3 py-1 border border-gray-300 rounded text-sm">
                                <option value="nse_cm">NSE Cash</option>
                                <option value="nse_fo">NSE F&O</option>
                                <option value="mcx_fo">MCX</option>
                            </select>
                            <select id="timeframe" class="px-3 py-1 border border-gray-300 rounded text-sm">
                                <option value="1m">1m</option>
                                <option value="5m">5m</option>
                                <option value="15m">15m</option>
                                <option value="1h">1h</option>
                                <option value="1d">1D</option>
                            </select>
                            <button id="subscribeBtn" class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600">
                                <i class="fas fa-play"></i> Subscribe
                            </button>
                        </div>
                    </div>
                    <div id="chartContainer" class="chart-container"></div>
                </div>

                <!-- Market Watch -->
                <div class="bg-white rounded-lg p-6 card-shadow">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Market Watch</h3>
                        <button id="addToWatchlist" class="text-blue-500 hover:text-blue-700">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <div id="marketWatch" class="space-y-3">
                        <!-- Market watch items will be populated here -->
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-chart-bar text-3xl mb-2"></i>
                            <p>No symbols in watchlist</p>
                            <p class="text-sm">Add symbols to monitor live prices</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Positions and Strategies -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                <!-- Current Positions -->
                <div class="bg-white rounded-lg p-6 card-shadow">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Current Positions</h3>
                        <button id="refreshPositions" class="text-blue-500 hover:text-blue-700">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div id="positionsTable" class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b">
                                    <th class="text-left py-2">Symbol</th>
                                    <th class="text-right py-2">Qty</th>
                                    <th class="text-right py-2">Avg Price</th>
                                    <th class="text-right py-2">LTP</th>
                                    <th class="text-right py-2">P&L</th>
                                </tr>
                            </thead>
                            <tbody id="positionsBody">
                                <!-- Positions will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Trading Strategies -->
                <div class="bg-white rounded-lg p-6 card-shadow">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Trading Strategies</h3>
                        <button id="addStrategy" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                            <i class="fas fa-plus"></i> Add Strategy
                        </button>
                    </div>
                    <div id="strategiesList" class="space-y-3">
                        <!-- Strategies will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Trade History -->
            <div class="bg-white rounded-lg p-6 card-shadow mt-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Recent Trades</h3>
                    <button id="refreshTrades" class="text-blue-500 hover:text-blue-700">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div id="tradesTable" class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left py-2">Time</th>
                                <th class="text-left py-2">Symbol</th>
                                <th class="text-left py-2">Type</th>
                                <th class="text-right py-2">Qty</th>
                                <th class="text-right py-2">Price</th>
                                <th class="text-right py-2">Status</th>
                            </tr>
                        </thead>
                        <tbody id="tradesBody">
                            <!-- Trades will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Strategy Modal -->
    <div id="strategyModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden modal">
        <div class="bg-white rounded-lg p-8 max-w-lg w-full mx-4 card-shadow">
            <h2 class="text-2xl font-bold mb-6">Add Trading Strategy</h2>
            <form id="strategyForm">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Symbol</label>
                        <input type="text" id="strategySymbol" class="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Exchange</label>
                        <select id="strategyExchange" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="nse_cm">NSE Cash</option>
                            <option value="nse_fo">NSE F&O</option>
                            <option value="mcx_fo">MCX</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                        <input type="number" id="strategyQuantity" class="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Entry Time</label>
                        <input type="time" id="strategyTime" class="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Upper Limit (%)</label>
                        <input type="number" id="strategyUpperLimit" step="0.1" class="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Lower Limit (%)</label>
                        <input type="number" id="strategyLowerLimit" step="0.1" class="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Product</label>
                        <select id="strategyProduct" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="MIS">MIS</option>
                            <option value="CNC">CNC</option>
                            <option value="NRML">NRML</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Transaction Type</label>
                        <select id="strategyTransactionType" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="BUY">BUY</option>
                            <option value="SELL">SELL</option>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" id="cancelStrategy" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">Cancel</button>
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add Strategy</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>