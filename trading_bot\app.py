#!/usr/bin/env python3
"""
Professional Kotak Neo Autotrading Bot
=====================================

A comprehensive trading bot with real-time portfolio monitoring, automated strategy execution,
and professional web interface for Kotak Neo API.

Features:
- Real-time portfolio and position tracking
- Live market data and candlestick charts
- Automated trading strategies with risk management
- Professional web dashboard
- Support for Equity, Options (Nifty/Bank Nifty), and MCX commodities

Author: AI Assistant
Version: 1.0.0
"""

import os
import sys
import json
import logging
import threading
import time
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from flask import Flask, render_template, request, jsonify, session
from flask_socketio import SocketIO, emit
import pandas as pd
import numpy as np
from werkzeug.security import generate_password_hash, check_password_hash

# Add the neo_api_client to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
import neo_api_client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class TradingCredentials:
    """Secure storage for trading credentials"""
    consumer_key: str
    consumer_secret: str
    mobile_number: str
    password: str
    environment: str = 'prod'  # 'prod' or 'uat'

@dataclass
class StrategyConfig:
    """Configuration for trading strategy"""
    id: Optional[int] = None
    symbol: str = ""
    exchange_segment: str = "nse_cm"
    quantity: int = 1
    entry_time: str = "09:30"  # HH:MM format
    upper_limit: float = 2.0  # Upper price movement limit (%)
    lower_limit: float = -2.0  # Lower price movement limit (%)
    product: str = 'MIS'  # MIS, CNC, NRML
    order_type: str = 'MARKET'  # MARKET, LIMIT
    transaction_type: str = 'BUY'  # BUY, SELL
    enabled: bool = True
    created_at: Optional[str] = None

@dataclass
class Position:
    """Position data structure"""
    symbol: str
    quantity: int
    average_price: float
    current_price: float
    pnl: float
    pnl_percentage: float
    exchange_segment: str = ""
    product: str = ""

class DatabaseManager:
    """Database manager for storing strategies and logs"""

    def __init__(self, db_path: str = "trading_bot.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """Initialize database tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create strategies table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS strategies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                exchange_segment TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                entry_time TEXT NOT NULL,
                upper_limit REAL NOT NULL,
                lower_limit REAL NOT NULL,
                product TEXT NOT NULL,
                order_type TEXT NOT NULL,
                transaction_type TEXT NOT NULL,
                enabled BOOLEAN NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Create trade logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trade_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_id INTEGER,
                symbol TEXT NOT NULL,
                action TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                price REAL,
                order_id TEXT,
                status TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (strategy_id) REFERENCES strategies (id)
            )
        ''')

        # Create settings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

    def add_strategy(self, strategy: StrategyConfig) -> int:
        """Add a new strategy"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO strategies (symbol, exchange_segment, quantity, entry_time,
                                  upper_limit, lower_limit, product, order_type,
                                  transaction_type, enabled)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (strategy.symbol, strategy.exchange_segment, strategy.quantity,
              strategy.entry_time, strategy.upper_limit, strategy.lower_limit,
              strategy.product, strategy.order_type, strategy.transaction_type,
              strategy.enabled))

        strategy_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return strategy_id

    def get_strategies(self) -> List[StrategyConfig]:
        """Get all strategies"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM strategies ORDER BY created_at DESC')
        rows = cursor.fetchall()
        conn.close()

        strategies = []
        for row in rows:
            strategy = StrategyConfig(
                id=row[0], symbol=row[1], exchange_segment=row[2],
                quantity=row[3], entry_time=row[4], upper_limit=row[5],
                lower_limit=row[6], product=row[7], order_type=row[8],
                transaction_type=row[9], enabled=bool(row[10]),
                created_at=row[11]
            )
            strategies.append(strategy)

        return strategies

    def update_strategy(self, strategy_id: int, enabled: bool):
        """Update strategy status"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('UPDATE strategies SET enabled = ? WHERE id = ?', (enabled, strategy_id))
        conn.commit()
        conn.close()

    def delete_strategy(self, strategy_id: int):
        """Delete a strategy"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('DELETE FROM strategies WHERE id = ?', (strategy_id,))
        conn.commit()
        conn.close()

    def log_trade(self, strategy_id: int, symbol: str, action: str,
                  quantity: int, price: float, order_id: str, status: str):
        """Log a trade"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO trade_logs (strategy_id, symbol, action, quantity, price, order_id, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (strategy_id, symbol, action, quantity, price, order_id, status))

        conn.commit()
        conn.close()

class KotakNeoAPI:
    """Enhanced Kotak Neo API wrapper with additional functionality"""

    def __init__(self, credentials: TradingCredentials):
        self.credentials = credentials
        self.client = None
        self.is_authenticated = False
        self.session_data = {}
        self.websocket_connected = False
        self.socketio = None

    def authenticate(self, otp: str = None) -> Dict[str, Any]:
        """
        Authenticate with Kotak Neo API

        Args:
            otp: One-time password for 2FA (if required)

        Returns:
            Authentication status and session data
        """
        try:
            # Initialize client
            self.client = neo_api_client.NeoAPI(
                consumer_key=self.credentials.consumer_key,
                consumer_secret=self.credentials.consumer_secret,
                environment=self.credentials.environment
            )

            # Set WebSocket callbacks after initialization
            self.client.on_message = self._on_websocket_message
            self.client.on_error = self._on_websocket_error
            self.client.on_open = self._on_websocket_open
            self.client.on_close = self._on_websocket_close

            # Login with mobile number and password
            login_response = self.client.login(
                mobilenumber=self.credentials.mobile_number,
                password=self.credentials.password
            )

            if 'error' in str(login_response).lower():
                return {'success': False, 'message': 'Login failed', 'data': login_response}

            # If OTP is provided, complete 2FA
            if otp:
                session_response = self.client.session_2fa(otp)
                if 'error' in str(session_response).lower():
                    return {'success': False, 'message': '2FA failed', 'data': session_response}

                self.session_data = session_response
                self.is_authenticated = True
                logger.info("Successfully authenticated with Kotak Neo API")
                return {'success': True, 'message': 'Authentication successful', 'data': session_response}
            else:
                return {'success': True, 'message': 'Login successful, OTP required', 'requires_otp': True}

        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return {'success': False, 'message': f'Authentication error: {str(e)}'}

    def _on_websocket_message(self, message):
        """Handle WebSocket messages"""
        try:
            # Process and emit to connected web clients
            if self.socketio:
                self.socketio.emit('market_data', message)
        except Exception as e:
            logger.error(f"WebSocket message error: {str(e)}")

    def _on_websocket_error(self, error):
        """Handle WebSocket errors"""
        logger.error(f"WebSocket error: {error}")
        self.websocket_connected = False

    def _on_websocket_open(self, message):
        """Handle WebSocket connection open"""
        logger.info("WebSocket connection opened")
        self.websocket_connected = True

    def _on_websocket_close(self, message):
        """Handle WebSocket connection close"""
        logger.info("WebSocket connection closed")
        self.websocket_connected = False

    def get_portfolio(self) -> Dict[str, Any]:
        """Get current portfolio holdings"""
        if not self.is_authenticated:
            return {'success': False, 'message': 'Not authenticated'}

        try:
            holdings = self.client.holdings()
            return {'success': True, 'data': holdings}
        except Exception as e:
            logger.error(f"Portfolio fetch error: {str(e)}")
            return {'success': False, 'message': str(e)}

    def get_positions(self) -> Dict[str, Any]:
        """Get current positions"""
        if not self.is_authenticated:
            return {'success': False, 'message': 'Not authenticated'}

        try:
            positions = self.client.positions()
            return {'success': True, 'data': positions}
        except Exception as e:
            logger.error(f"Positions fetch error: {str(e)}")
            return {'success': False, 'message': str(e)}

    def get_trade_report(self) -> Dict[str, Any]:
        """Get trade report"""
        if not self.is_authenticated:
            return {'success': False, 'message': 'Not authenticated'}

        try:
            trades = self.client.trade_report()
            return {'success': True, 'data': trades}
        except Exception as e:
            logger.error(f"Trade report fetch error: {str(e)}")
            return {'success': False, 'message': str(e)}

    def get_limits(self) -> Dict[str, Any]:
        """Get account limits"""
        if not self.is_authenticated:
            return {'success': False, 'message': 'Not authenticated'}

        try:
            limits = self.client.limits()
            return {'success': True, 'data': limits}
        except Exception as e:
            logger.error(f"Limits fetch error: {str(e)}")
            return {'success': False, 'message': str(e)}

    def search_symbol(self, exchange_segment: str, symbol: str, **kwargs) -> Dict[str, Any]:
        """Search for trading symbols"""
        if not self.is_authenticated:
            return {'success': False, 'message': 'Not authenticated'}

        try:
            result = self.client.search_scrip(
                exchange_segment=exchange_segment,
                symbol=symbol,
                **kwargs
            )
            return {'success': True, 'data': result}
        except Exception as e:
            logger.error(f"Symbol search error: {str(e)}")
            return {'success': False, 'message': str(e)}

    def get_quotes(self, instrument_tokens: List[Dict]) -> Dict[str, Any]:
        """Get quotes for instruments"""
        if not self.is_authenticated:
            return {'success': False, 'message': 'Not authenticated'}

        try:
            quotes = self.client.quotes(instrument_tokens=instrument_tokens)
            return {'success': True, 'data': quotes}
        except Exception as e:
            logger.error(f"Quotes fetch error: {str(e)}")
            return {'success': False, 'message': str(e)}

    def place_order(self, **order_params) -> Dict[str, Any]:
        """Place a trading order"""
        if not self.is_authenticated:
            return {'success': False, 'message': 'Not authenticated'}

        try:
            result = self.client.place_order(**order_params)
            logger.info(f"Order placed: {result}")
            return {'success': True, 'data': result}
        except Exception as e:
            logger.error(f"Order placement error: {str(e)}")
            return {'success': False, 'message': str(e)}

    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an order"""
        if not self.is_authenticated:
            return {'success': False, 'message': 'Not authenticated'}

        try:
            result = self.client.cancel_order(order_id=order_id)
            logger.info(f"Order cancelled: {result}")
            return {'success': True, 'data': result}
        except Exception as e:
            logger.error(f"Order cancellation error: {str(e)}")
            return {'success': False, 'message': str(e)}

    def subscribe_to_live_data(self, instrument_tokens: List[Dict]) -> Dict[str, Any]:
        """Subscribe to live market data"""
        if not self.is_authenticated:
            return {'success': False, 'message': 'Not authenticated'}

        try:
            self.client.subscribe(instrument_tokens=instrument_tokens)
            return {'success': True, 'message': 'Subscribed to live data'}
        except Exception as e:
            logger.error(f"Live data subscription error: {str(e)}")
            return {'success': False, 'message': str(e)}

class TradingStrategy:
    """Automated trading strategy engine"""

    def __init__(self, api_client: KotakNeoAPI, db_manager: DatabaseManager):
        self.api_client = api_client
        self.db_manager = db_manager
        self.strategies: List[StrategyConfig] = []
        self.active_positions: Dict[str, Position] = {}
        self.monitoring_thread = None
        self.is_monitoring = False
        self.price_cache = {}

    def load_strategies(self):
        """Load strategies from database"""
        self.strategies = self.db_manager.get_strategies()
        logger.info(f"Loaded {len(self.strategies)} strategies")

    def add_strategy(self, strategy: StrategyConfig) -> int:
        """Add a new trading strategy"""
        strategy_id = self.db_manager.add_strategy(strategy)
        strategy.id = strategy_id
        self.strategies.append(strategy)
        logger.info(f"Added strategy for {strategy.symbol}")
        return strategy_id

    def remove_strategy(self, strategy_id: int):
        """Remove a trading strategy"""
        self.db_manager.delete_strategy(strategy_id)
        self.strategies = [s for s in self.strategies if s.id != strategy_id]
        logger.info(f"Removed strategy {strategy_id}")

    def toggle_strategy(self, strategy_id: int, enabled: bool):
        """Enable/disable a strategy"""
        self.db_manager.update_strategy(strategy_id, enabled)
        for strategy in self.strategies:
            if strategy.id == strategy_id:
                strategy.enabled = enabled
                break
        logger.info(f"Strategy {strategy_id} {'enabled' if enabled else 'disabled'}")

    def start_monitoring(self):
        """Start monitoring strategies and positions"""
        if self.is_monitoring:
            return

        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        logger.info("Strategy monitoring started")

    def stop_monitoring(self):
        """Stop monitoring strategies"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join()
        logger.info("Strategy monitoring stopped")

    def _monitoring_loop(self):
        """Main monitoring loop for strategies"""
        while self.is_monitoring:
            try:
                current_time = datetime.now().strftime("%H:%M")

                # Check for scheduled entries
                for strategy in self.strategies:
                    if strategy.enabled and strategy.entry_time == current_time:
                        self._execute_entry(strategy)

                # Monitor existing positions for exit conditions
                self._monitor_positions()

                time.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"Monitoring loop error: {str(e)}")
                time.sleep(60)

    def _execute_entry(self, strategy: StrategyConfig):
        """Execute entry order for strategy"""
        try:
            # Search for instrument token
            search_result = self.api_client.search_symbol(
                exchange_segment=strategy.exchange_segment,
                symbol=strategy.symbol
            )

            if not search_result['success']:
                logger.error(f"Symbol search failed for {strategy.symbol}")
                return

            # Place order
            order_result = self.api_client.place_order(
                exchange_segment=strategy.exchange_segment,
                product=strategy.product,
                price="0",  # Market order
                order_type=strategy.order_type,
                quantity=str(strategy.quantity),
                validity="DAY",
                trading_symbol=strategy.symbol,
                transaction_type=strategy.transaction_type,
                amo="NO"
            )

            if order_result['success']:
                order_id = order_result.get('data', {}).get('nOrdNo', '')
                self.db_manager.log_trade(
                    strategy.id, strategy.symbol, 'ENTRY',
                    strategy.quantity, 0.0, order_id, 'PLACED'
                )
                logger.info(f"Entry order placed for {strategy.symbol}")
            else:
                logger.error(f"Entry order failed for {strategy.symbol}: {order_result['message']}")

        except Exception as e:
            logger.error(f"Entry execution error for {strategy.symbol}: {str(e)}")

    def _monitor_positions(self):
        """Monitor positions for exit conditions"""
        try:
            positions_result = self.api_client.get_positions()
            if not positions_result['success']:
                return

            positions_data = positions_result.get('data', {}).get('data', [])

            for pos_data in positions_data:
                symbol = pos_data.get('trdSym', '')
                net_qty = self._calculate_net_quantity(pos_data)

                if net_qty != 0:  # Active position
                    # Check if this position has an associated strategy
                    strategy = self._find_strategy_for_symbol(symbol)
                    if strategy:
                        self._check_exit_conditions(strategy, pos_data, net_qty)

        except Exception as e:
            logger.error(f"Position monitoring error: {str(e)}")

    def _calculate_net_quantity(self, pos_data: Dict) -> int:
        """Calculate net quantity from position data"""
        try:
            cf_buy_qty = int(pos_data.get('cfBuyQty', 0))
            cf_sell_qty = int(pos_data.get('cfSellQty', 0))
            fl_buy_qty = int(pos_data.get('flBuyQty', 0))
            fl_sell_qty = int(pos_data.get('flSellQty', 0))

            total_buy = cf_buy_qty + fl_buy_qty
            total_sell = cf_sell_qty + fl_sell_qty

            return total_buy - total_sell
        except:
            return 0

    def _find_strategy_for_symbol(self, symbol: str) -> Optional[StrategyConfig]:
        """Find strategy for a given symbol"""
        for strategy in self.strategies:
            if strategy.enabled and strategy.symbol in symbol:
                return strategy
        return None

    def _check_exit_conditions(self, strategy: StrategyConfig, pos_data: Dict, net_qty: int):
        """Check if exit conditions are met"""
        try:
            # Get current price (this would need to be implemented with live data)
            current_price = float(pos_data.get('ltp', 0))
            avg_price = self._calculate_average_price(pos_data)

            if current_price > 0 and avg_price > 0:
                price_change_pct = ((current_price - avg_price) / avg_price) * 100

                # Check exit conditions
                should_exit = False
                if net_qty > 0:  # Long position
                    if price_change_pct >= strategy.upper_limit or price_change_pct <= strategy.lower_limit:
                        should_exit = True
                elif net_qty < 0:  # Short position
                    if price_change_pct <= -strategy.upper_limit or price_change_pct >= -strategy.lower_limit:
                        should_exit = True

                if should_exit:
                    self._execute_exit(strategy, pos_data, abs(net_qty))

        except Exception as e:
            logger.error(f"Exit condition check error: {str(e)}")

    def _calculate_average_price(self, pos_data: Dict) -> float:
        """Calculate average price from position data"""
        try:
            total_buy_amt = float(pos_data.get('cfBuyAmt', 0)) + float(pos_data.get('buyAmt', 0))
            total_sell_amt = float(pos_data.get('cfSellAmt', 0)) + float(pos_data.get('sellAmt', 0))

            cf_buy_qty = int(pos_data.get('cfBuyQty', 0))
            cf_sell_qty = int(pos_data.get('cfSellQty', 0))
            fl_buy_qty = int(pos_data.get('flBuyQty', 0))
            fl_sell_qty = int(pos_data.get('flSellQty', 0))

            total_buy_qty = cf_buy_qty + fl_buy_qty
            total_sell_qty = cf_sell_qty + fl_sell_qty

            if total_buy_qty > total_sell_qty and total_buy_qty > 0:
                return total_buy_amt / total_buy_qty
            elif total_sell_qty > total_buy_qty and total_sell_qty > 0:
                return total_sell_amt / total_sell_qty

            return 0.0
        except:
            return 0.0

    def _execute_exit(self, strategy: StrategyConfig, pos_data: Dict, quantity: int):
        """Execute exit order"""
        try:
            # Determine transaction type for exit
            transaction_type = 'SELL' if strategy.transaction_type == 'BUY' else 'BUY'

            order_result = self.api_client.place_order(
                exchange_segment=strategy.exchange_segment,
                product=strategy.product,
                price="0",  # Market order
                order_type="MARKET",
                quantity=str(quantity),
                validity="DAY",
                trading_symbol=pos_data.get('trdSym', ''),
                transaction_type=transaction_type,
                amo="NO"
            )

            if order_result['success']:
                order_id = order_result.get('data', {}).get('nOrdNo', '')
                self.db_manager.log_trade(
                    strategy.id, strategy.symbol, 'EXIT',
                    quantity, 0.0, order_id, 'PLACED'
                )
                logger.info(f"Exit order placed for {strategy.symbol}")
            else:
                logger.error(f"Exit order failed for {strategy.symbol}: {order_result['message']}")

        except Exception as e:
            logger.error(f"Exit execution error for {strategy.symbol}: {str(e)}")

# Flask application setup
app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global instances
db_manager = DatabaseManager()
api_client = None
trading_strategy = None

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('index.html')

@app.route('/api/authenticate', methods=['POST'])
def authenticate():
    """API endpoint for authentication"""
    global api_client, trading_strategy

    try:
        data = request.json
        credentials = TradingCredentials(
            consumer_key=data.get('consumer_key'),
            consumer_secret=data.get('consumer_secret'),
            mobile_number=data.get('mobile_number'),
            password=data.get('password'),
            environment=data.get('environment', 'prod')
        )

        api_client = KotakNeoAPI(credentials)
        api_client.socketio = socketio  # For WebSocket message forwarding

        result = api_client.authenticate(data.get('otp'))

        if result['success'] and not result.get('requires_otp'):
            trading_strategy = TradingStrategy(api_client, db_manager)
            trading_strategy.load_strategies()
            trading_strategy.start_monitoring()

        return jsonify(result)
    except Exception as e:
        logger.error(f"Authentication endpoint error: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/portfolio', methods=['GET'])
def get_portfolio():
    """Get portfolio holdings"""
    if not api_client or not api_client.is_authenticated:
        return jsonify({'success': False, 'message': 'Not authenticated'})

    result = api_client.get_portfolio()
    return jsonify(result)

@app.route('/api/positions', methods=['GET'])
def get_positions():
    """Get current positions"""
    if not api_client or not api_client.is_authenticated:
        return jsonify({'success': False, 'message': 'Not authenticated'})

    result = api_client.get_positions()
    return jsonify(result)

@app.route('/api/trades', methods=['GET'])
def get_trades():
    """Get trade report"""
    if not api_client or not api_client.is_authenticated:
        return jsonify({'success': False, 'message': 'Not authenticated'})

    result = api_client.get_trade_report()
    return jsonify(result)

@app.route('/api/limits', methods=['GET'])
def get_limits():
    """Get account limits"""
    if not api_client or not api_client.is_authenticated:
        return jsonify({'success': False, 'message': 'Not authenticated'})

    result = api_client.get_limits()
    return jsonify(result)

@app.route('/api/search', methods=['POST'])
def search_symbols():
    """Search for symbols"""
    if not api_client or not api_client.is_authenticated:
        return jsonify({'success': False, 'message': 'Not authenticated'})

    data = request.json
    result = api_client.search_symbol(
        exchange_segment=data.get('exchange_segment'),
        symbol=data.get('symbol'),
        expiry=data.get('expiry', ''),
        option_type=data.get('option_type', ''),
        strike_price=data.get('strike_price', '')
    )
    return jsonify(result)

@app.route('/api/quotes', methods=['POST'])
def get_quotes():
    """Get quotes for instruments"""
    if not api_client or not api_client.is_authenticated:
        return jsonify({'success': False, 'message': 'Not authenticated'})

    data = request.json
    instrument_tokens = data.get('instrument_tokens', [])
    result = api_client.get_quotes(instrument_tokens)
    return jsonify(result)

@app.route('/api/strategies', methods=['GET'])
def get_strategies():
    """Get all strategies"""
    if not trading_strategy:
        return jsonify({'success': False, 'message': 'Trading engine not initialized'})

    strategies = [asdict(s) for s in trading_strategy.strategies]
    return jsonify({'success': True, 'data': strategies})

@app.route('/api/strategies', methods=['POST'])
def add_strategy():
    """Add a new strategy"""
    if not trading_strategy:
        return jsonify({'success': False, 'message': 'Trading engine not initialized'})

    try:
        data = request.json
        strategy = StrategyConfig(
            symbol=data.get('symbol'),
            exchange_segment=data.get('exchange_segment'),
            quantity=int(data.get('quantity')),
            entry_time=data.get('entry_time'),
            upper_limit=float(data.get('upper_limit')),
            lower_limit=float(data.get('lower_limit')),
            product=data.get('product', 'MIS'),
            order_type=data.get('order_type', 'MARKET'),
            transaction_type=data.get('transaction_type', 'BUY'),
            enabled=data.get('enabled', True)
        )

        strategy_id = trading_strategy.add_strategy(strategy)
        return jsonify({'success': True, 'strategy_id': strategy_id})
    except Exception as e:
        logger.error(f"Add strategy error: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/strategies/<int:strategy_id>', methods=['PUT'])
def update_strategy(strategy_id):
    """Update strategy status"""
    if not trading_strategy:
        return jsonify({'success': False, 'message': 'Trading engine not initialized'})

    try:
        data = request.json
        enabled = data.get('enabled', True)
        trading_strategy.toggle_strategy(strategy_id, enabled)
        return jsonify({'success': True})
    except Exception as e:
        logger.error(f"Update strategy error: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/strategies/<int:strategy_id>', methods=['DELETE'])
def delete_strategy(strategy_id):
    """Delete a strategy"""
    if not trading_strategy:
        return jsonify({'success': False, 'message': 'Trading engine not initialized'})

    try:
        trading_strategy.remove_strategy(strategy_id)
        return jsonify({'success': True})
    except Exception as e:
        logger.error(f"Delete strategy error: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/subscribe', methods=['POST'])
def subscribe_live_data():
    """Subscribe to live market data"""
    if not api_client or not api_client.is_authenticated:
        return jsonify({'success': False, 'message': 'Not authenticated'})

    data = request.json
    instrument_tokens = data.get('instrument_tokens', [])
    result = api_client.subscribe_to_live_data(instrument_tokens)
    return jsonify(result)

# SocketIO Events
@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info('Client connected')
    emit('status', {'message': 'Connected to trading bot'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info('Client disconnected')

@socketio.on('subscribe_symbols')
def handle_subscribe_symbols(data):
    """Handle symbol subscription requests"""
    if api_client and api_client.is_authenticated:
        instrument_tokens = data.get('symbols', [])
        result = api_client.subscribe_to_live_data(instrument_tokens)
        emit('subscription_status', result)
    else:
        emit('subscription_status', {'success': False, 'message': 'Not authenticated'})

@socketio.on('get_portfolio_update')
def handle_portfolio_update():
    """Handle portfolio update requests"""
    if api_client and api_client.is_authenticated:
        portfolio = api_client.get_portfolio()
        positions = api_client.get_positions()
        trades = api_client.get_trade_report()
        limits = api_client.get_limits()

        emit('portfolio_update', {
            'portfolio': portfolio,
            'positions': positions,
            'trades': trades,
            'limits': limits
        })
    else:
        emit('portfolio_update', {'error': 'Not authenticated'})

def calculate_portfolio_summary(portfolio_data, positions_data):
    """Calculate portfolio summary statistics"""
    try:
        total_value = 0.0
        total_pnl = 0.0
        active_positions = 0

        # Calculate from holdings
        if portfolio_data.get('success') and portfolio_data.get('data'):
            holdings = portfolio_data['data'].get('data', [])
            for holding in holdings:
                market_value = float(holding.get('mktValue', 0))
                holding_cost = float(holding.get('holdingCost', 0))
                total_value += market_value
                total_pnl += (market_value - holding_cost)

        # Calculate from positions
        if positions_data.get('success') and positions_data.get('data'):
            positions = positions_data['data'].get('data', [])
            for position in positions:
                net_qty = calculate_net_qty_from_position(position)
                if net_qty != 0:
                    active_positions += 1

        return {
            'total_value': total_value,
            'total_pnl': total_pnl,
            'active_positions': active_positions
        }
    except Exception as e:
        logger.error(f"Portfolio summary calculation error: {str(e)}")
        return {'total_value': 0, 'total_pnl': 0, 'active_positions': 0}

def calculate_net_qty_from_position(position):
    """Calculate net quantity from position data"""
    try:
        cf_buy_qty = int(position.get('cfBuyQty', 0))
        cf_sell_qty = int(position.get('cfSellQty', 0))
        fl_buy_qty = int(position.get('flBuyQty', 0))
        fl_sell_qty = int(position.get('flSellQty', 0))

        total_buy = cf_buy_qty + fl_buy_qty
        total_sell = cf_sell_qty + fl_sell_qty

        return total_buy - total_sell
    except:
        return 0

# Background task to send periodic updates
def background_updates():
    """Send periodic updates to connected clients"""
    while True:
        try:
            if api_client and api_client.is_authenticated:
                # Get latest data
                portfolio = api_client.get_portfolio()
                positions = api_client.get_positions()

                # Calculate summary
                summary = calculate_portfolio_summary(portfolio, positions)

                # Emit updates
                socketio.emit('portfolio_summary', summary)

                # Emit strategy status
                if trading_strategy:
                    strategies = [asdict(s) for s in trading_strategy.strategies]
                    socketio.emit('strategies_update', {'strategies': strategies})

            time.sleep(30)  # Update every 30 seconds
        except Exception as e:
            logger.error(f"Background update error: {str(e)}")
            time.sleep(30)

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)

    # Start background update thread
    update_thread = threading.Thread(target=background_updates)
    update_thread.daemon = True
    update_thread.start()

    logger.info("Starting Professional Kotak Neo Trading Bot")
    logger.info("Access the application at: http://localhost:5000")

    # Run the application
    socketio.run(app, debug=False, host='0.0.0.0', port=5000)

