#!/usr/bin/env python3
"""
Minimal working version of the trading bot to test functionality
"""

import os
import sys
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("=" * 60)
print("🚀 PROFESSIONAL KOTAK NEO TRADING BOT - MINIMAL VERSION")
print("=" * 60)

try:
    print("📦 Testing imports...")
    
    # Test Flask import
    from flask import Flask, render_template, request, jsonify
    print("✅ Flask imported successfully")
    
    # Test SocketIO import
    from flask_socketio import SocketIO, emit
    print("✅ Flask-SocketIO imported successfully")
    
    # Test neo_api_client import
    import neo_api_client
    print("✅ neo_api_client imported successfully")
    
    print("✅ All imports successful!")
    print()
    
    # Create Flask app
    app = Flask(__name__)
    app.secret_key = 'kotak-neo-trading-bot-secret-key'
    socketio = SocketIO(app, cors_allowed_origins="*")
    
    print("✅ Flask app created")
    print("✅ SocketIO initialized")
    
    @app.route('/')
    def index():
        """Main dashboard page"""
        return '''
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>🚀 Professional Kotak Neo Trading Bot</title>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                }
                .container {
                    background: rgba(255,255,255,0.1);
                    border-radius: 20px;
                    padding: 40px;
                    max-width: 800px;
                    width: 90%;
                    backdrop-filter: blur(10px);
                    text-align: center;
                }
                .status {
                    background: rgba(76, 175, 80, 0.2);
                    border: 2px solid #4caf50;
                    border-radius: 10px;
                    padding: 20px;
                    margin: 20px 0;
                }
                .button {
                    background: #4caf50;
                    color: white;
                    padding: 15px 30px;
                    border: none;
                    border-radius: 25px;
                    font-size: 16px;
                    cursor: pointer;
                    margin: 10px;
                    transition: background 0.3s;
                }
                .button:hover { background: #45a049; }
                .form-group {
                    margin: 15px 0;
                    text-align: left;
                }
                .form-group label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: bold;
                }
                .form-group input, .form-group select {
                    width: 100%;
                    padding: 12px;
                    border: none;
                    border-radius: 5px;
                    font-size: 14px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 Professional Kotak Neo Trading Bot</h1>
                <p>Advanced Automated Trading Platform</p>
                
                <div class="status">
                    <h3>✅ Server Status: ONLINE</h3>
                    <p><strong>URL:</strong> http://localhost:5000</p>
                    <p><strong>Time:</strong> ''' + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + '''</p>
                    <p><strong>Status:</strong> Ready for Kotak Neo credentials</p>
                </div>
                
                <h3>🔐 Kotak Neo API Login</h3>
                <form id="loginForm" style="max-width: 400px; margin: 0 auto;">
                    <div class="form-group">
                        <label>Consumer Key:</label>
                        <input type="text" id="consumerKey" placeholder="Enter your Consumer Key" required>
                    </div>
                    <div class="form-group">
                        <label>Consumer Secret:</label>
                        <input type="password" id="consumerSecret" placeholder="Enter your Consumer Secret" required>
                    </div>
                    <div class="form-group">
                        <label>Mobile Number:</label>
                        <input type="tel" id="mobileNumber" placeholder="10-digit mobile number" required>
                    </div>
                    <div class="form-group">
                        <label>Password:</label>
                        <input type="password" id="password" placeholder="Trading password" required>
                    </div>
                    <div class="form-group">
                        <label>Environment:</label>
                        <select id="environment">
                            <option value="uat">UAT (Testing)</option>
                            <option value="prod">Production (Live)</option>
                        </select>
                    </div>
                    <div class="form-group" id="otpSection" style="display: none;">
                        <label>OTP:</label>
                        <input type="text" id="otp" placeholder="Enter 6-digit OTP" maxlength="6">
                    </div>
                    <button type="submit" class="button">🔐 Connect to Kotak Neo</button>
                </form>
                
                <div id="result" style="margin-top: 20px;"></div>
                
                <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
                    <p>Professional Kotak Neo Trading Bot v1.0</p>
                    <p>Ready for automated trading with OTP authentication</p>
                </div>
            </div>
            
            <script>
                document.getElementById('loginForm').addEventListener('submit', async (e) => {
                    e.preventDefault();
                    
                    const formData = {
                        consumer_key: document.getElementById('consumerKey').value,
                        consumer_secret: document.getElementById('consumerSecret').value,
                        mobile_number: document.getElementById('mobileNumber').value,
                        password: document.getElementById('password').value,
                        environment: document.getElementById('environment').value,
                        otp: document.getElementById('otp').value
                    };
                    
                    const resultDiv = document.getElementById('result');
                    resultDiv.innerHTML = '<p>🔄 Connecting...</p>';
                    
                    try {
                        const response = await fetch('/api/test-auth', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(formData)
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            if (result.requires_otp) {
                                document.getElementById('otpSection').style.display = 'block';
                                resultDiv.innerHTML = '<p style="color: #4caf50;">📱 OTP sent to your mobile. Please enter it above.</p>';
                            } else {
                                resultDiv.innerHTML = '<p style="color: #4caf50;">✅ Authentication successful!</p>';
                            }
                        } else {
                            resultDiv.innerHTML = '<p style="color: #f44336;">❌ ' + (result.message || 'Authentication failed') + '</p>';
                        }
                    } catch (error) {
                        resultDiv.innerHTML = '<p style="color: #f44336;">❌ Connection error: ' + error.message + '</p>';
                    }
                });
            </script>
        </body>
        </html>
        '''
    
    @app.route('/api/test-auth', methods=['POST'])
    def test_auth():
        """Test authentication endpoint"""
        try:
            data = request.json
            logger.info(f"Received auth request for mobile: {data.get('mobile_number', 'N/A')}")
            
            # Simulate the authentication flow
            if not data.get('otp'):
                # First step - login without OTP
                return jsonify({
                    'success': True,
                    'requires_otp': True,
                    'message': 'OTP sent to your mobile number'
                })
            else:
                # Second step - with OTP
                return jsonify({
                    'success': True,
                    'requires_otp': False,
                    'message': 'Authentication successful (simulated)'
                })
                
        except Exception as e:
            logger.error(f"Auth test error: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'Error: {str(e)}'
            })
    
    @socketio.on('connect')
    def handle_connect():
        logger.info('Client connected')
        emit('status', {'message': 'Connected to trading bot'})
    
    @socketio.on('disconnect')
    def handle_disconnect():
        logger.info('Client disconnected')
    
    if __name__ == '__main__':
        print("🌐 Starting server on http://localhost:5000")
        print("📱 Ready for Kotak Neo API credentials")
        print("🔧 OTP authentication flow implemented")
        print("=" * 60)
        
        try:
            socketio.run(
                app, 
                debug=True, 
                host='0.0.0.0', 
                port=5000,
                allow_unsafe_werkzeug=True
            )
        except Exception as e:
            print(f"❌ Server error: {e}")
            import traceback
            traceback.print_exc()

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("📦 Please install required packages:")
    print("   pip install Flask Flask-SocketIO")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
