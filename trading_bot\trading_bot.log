2025-07-08 17:25:28,197 - app - INFO - Added strategy for BANKNIFTY
2025-07-08 17:25:28,212 - app - INFO - Removed strategy 1
2025-07-08 17:51:57,324 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-08 17:51:57,324 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-08 17:51:57,448 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-08 17:51:57,448 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 17:52:30,192 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:30] "GET / HTTP/1.1" 200 -
2025-07-08 17:52:30,317 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:30] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-08 17:52:32,059 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:32] "GET /socket.io/?EIO=4&transport=polling&t=PVf_Djq HTTP/1.1" 200 -
2025-07-08 17:52:32,435 - __main__ - INFO - Client connected
2025-07-08 17:52:32,435 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:32] "POST /socket.io/?EIO=4&transport=polling&t=PVf_Dkz&sid=SQGyNPCVwPPPH1pAAAAA HTTP/1.1" 200 -
2025-07-08 17:52:32,435 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:32] "GET /socket.io/?EIO=4&transport=polling&t=PVf_Dl0&sid=SQGyNPCVwPPPH1pAAAAA HTTP/1.1" 200 -
2025-07-08 17:52:32,688 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:32] "GET /socket.io/?EIO=4&transport=polling&t=PVf_Dq0&sid=SQGyNPCVwPPPH1pAAAAA HTTP/1.1" 200 -
2025-07-08 17:52:32,816 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:32] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-08 17:54:38,745 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-08 17:54:38,749 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-08 17:54:38,774 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-08 17:54:38,774 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 17:55:22,772 - __main__ - ERROR - Authentication error: NeoAPI.__init__() got an unexpected keyword argument 'on_message'
2025-07-08 17:55:22,773 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:55:22] "POST /api/authenticate HTTP/1.1" 200 -
2025-07-08 17:55:29,829 - __main__ - ERROR - Authentication error: NeoAPI.__init__() got an unexpected keyword argument 'on_message'
2025-07-08 17:55:29,829 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:55:29] "POST /api/authenticate HTTP/1.1" 200 -
2025-07-08 17:58:57,350 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-08 17:58:57,350 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-08 17:58:57,381 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-08 17:58:57,427 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 17:59:27,497 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:59:27] "GET / HTTP/1.1" 200 -
2025-07-08 17:59:27,613 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:59:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-08 17:59:27,846 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:59:27] "GET /socket.io/?EIO=4&transport=polling&t=PVg0pEY HTTP/1.1" 200 -
2025-07-08 17:59:28,188 - __main__ - INFO - Client connected
2025-07-08 17:59:28,190 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:59:28] "POST /socket.io/?EIO=4&transport=polling&t=PVg0pFA&sid=OilGtAvmOHlxS5j-AAAC HTTP/1.1" 200 -
2025-07-08 17:59:28,219 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:59:28] "GET /socket.io/?EIO=4&transport=polling&t=PVg0pFC&sid=OilGtAvmOHlxS5j-AAAC HTTP/1.1" 200 -
2025-07-08 18:00:07,277 - __main__ - ERROR - Authentication error: NeoAPI.__init__() got an unexpected keyword argument 'on_message'
2025-07-08 18:00:07,278 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 18:00:07] "POST /api/authenticate HTTP/1.1" 200 -
2025-07-08 18:00:19,217 - __main__ - INFO - Client disconnected
2025-07-08 18:00:19,218 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 18:00:19] "GET /socket.io/?EIO=4&transport=websocket&sid=OilGtAvmOHlxS5j-AAAC HTTP/1.1" 200 -
2025-07-08 18:00:20,496 - __main__ - INFO - Client disconnected
2025-07-08 18:00:20,497 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 18:00:20] "GET /socket.io/?EIO=4&transport=websocket&sid=SQGyNPCVwPPPH1pAAAAA HTTP/1.1" 200 -
