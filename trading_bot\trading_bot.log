2025-07-08 17:25:28,197 - app - INFO - Added strategy for BANKNIFTY
2025-07-08 17:25:28,212 - app - INFO - Removed strategy 1
2025-07-08 17:51:57,324 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-08 17:51:57,324 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-08 17:51:57,448 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-08 17:51:57,448 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 17:52:30,192 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:30] "GET / HTTP/1.1" 200 -
2025-07-08 17:52:30,317 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:30] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-08 17:52:32,059 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:32] "GET /socket.io/?EIO=4&transport=polling&t=PVf_Djq HTTP/1.1" 200 -
2025-07-08 17:52:32,435 - __main__ - INFO - Client connected
2025-07-08 17:52:32,435 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:32] "POST /socket.io/?EIO=4&transport=polling&t=PVf_Dkz&sid=SQGyNPCVwPPPH1pAAAAA HTTP/1.1" 200 -
2025-07-08 17:52:32,435 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:32] "GET /socket.io/?EIO=4&transport=polling&t=PVf_Dl0&sid=SQGyNPCVwPPPH1pAAAAA HTTP/1.1" 200 -
2025-07-08 17:52:32,688 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:32] "GET /socket.io/?EIO=4&transport=polling&t=PVf_Dq0&sid=SQGyNPCVwPPPH1pAAAAA HTTP/1.1" 200 -
2025-07-08 17:52:32,816 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:52:32] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-08 17:54:38,745 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-08 17:54:38,749 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-08 17:54:38,774 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-08 17:54:38,774 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 17:55:22,772 - __main__ - ERROR - Authentication error: NeoAPI.__init__() got an unexpected keyword argument 'on_message'
2025-07-08 17:55:22,773 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:55:22] "POST /api/authenticate HTTP/1.1" 200 -
2025-07-08 17:55:29,829 - __main__ - ERROR - Authentication error: NeoAPI.__init__() got an unexpected keyword argument 'on_message'
2025-07-08 17:55:29,829 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:55:29] "POST /api/authenticate HTTP/1.1" 200 -
2025-07-08 17:58:57,350 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-08 17:58:57,350 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-08 17:58:57,381 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-08 17:58:57,427 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 17:59:27,497 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:59:27] "GET / HTTP/1.1" 200 -
2025-07-08 17:59:27,613 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:59:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-08 17:59:27,846 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:59:27] "GET /socket.io/?EIO=4&transport=polling&t=PVg0pEY HTTP/1.1" 200 -
2025-07-08 17:59:28,188 - __main__ - INFO - Client connected
2025-07-08 17:59:28,190 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:59:28] "POST /socket.io/?EIO=4&transport=polling&t=PVg0pFA&sid=OilGtAvmOHlxS5j-AAAC HTTP/1.1" 200 -
2025-07-08 17:59:28,219 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 17:59:28] "GET /socket.io/?EIO=4&transport=polling&t=PVg0pFC&sid=OilGtAvmOHlxS5j-AAAC HTTP/1.1" 200 -
2025-07-08 18:00:07,277 - __main__ - ERROR - Authentication error: NeoAPI.__init__() got an unexpected keyword argument 'on_message'
2025-07-08 18:00:07,278 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 18:00:07] "POST /api/authenticate HTTP/1.1" 200 -
2025-07-08 18:00:19,217 - __main__ - INFO - Client disconnected
2025-07-08 18:00:19,218 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 18:00:19] "GET /socket.io/?EIO=4&transport=websocket&sid=OilGtAvmOHlxS5j-AAAC HTTP/1.1" 200 -
2025-07-08 18:00:20,496 - __main__ - INFO - Client disconnected
2025-07-08 18:00:20,497 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 18:00:20] "GET /socket.io/?EIO=4&transport=websocket&sid=SQGyNPCVwPPPH1pAAAAA HTTP/1.1" 200 -
2025-07-11 17:42:23,293 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-11 17:42:23,293 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-11 17:42:23,323 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-11 17:42:23,324 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-11 17:42:23,324 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-11 17:42:24,691 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-11 17:42:24,691 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-11 17:42:24,691 - werkzeug - WARNING -  * Debugger is active!
2025-07-11 17:42:24,707 - werkzeug - INFO -  * Debugger PIN: 338-597-418
2025-07-11 17:42:26,289 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:26] "GET /socket.io/?EIO=4&transport=polling&t=PVvPg__ HTTP/1.1" 200 -
2025-07-11 17:42:26,566 - __main__ - INFO - Client connected
2025-07-11 17:42:26,568 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:26] "POST /socket.io/?EIO=4&transport=polling&t=PVvPh4q&sid=tMLGRr3sGUCwoPpTAAAA HTTP/1.1" 200 -
2025-07-11 17:42:26,609 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:26] "GET /socket.io/?EIO=4&transport=polling&t=PVvPh4r&sid=tMLGRr3sGUCwoPpTAAAA HTTP/1.1" 200 -
2025-07-11 17:42:26,616 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:26] "GET /socket.io/?EIO=4&transport=polling&t=PVvPh9t&sid=tMLGRr3sGUCwoPpTAAAA HTTP/1.1" 200 -
2025-07-11 17:42:34,196 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:34] "GET / HTTP/1.1" 200 -
2025-07-11 17:42:34,261 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 17:42:34,455 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:34] "GET /socket.io/?EIO=4&transport=polling&t=PVvPj2b HTTP/1.1" 200 -
2025-07-11 17:42:34,770 - __main__ - INFO - Client connected
2025-07-11 17:42:34,770 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:34] "POST /socket.io/?EIO=4&transport=polling&t=PVvPj4Q&sid=r7DS38az4J1yV5npAAAC HTTP/1.1" 200 -
2025-07-11 17:42:34,770 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:34] "GET /socket.io/?EIO=4&transport=polling&t=PVvPj4S&sid=r7DS38az4J1yV5npAAAC HTTP/1.1" 200 -
2025-07-11 17:42:36,756 - __main__ - INFO - Client disconnected
2025-07-11 17:42:36,756 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:36] "GET /socket.io/?EIO=4&transport=websocket&sid=tMLGRr3sGUCwoPpTAAAA HTTP/1.1" 200 -
2025-07-11 17:42:38,928 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:38] "GET / HTTP/1.1" 200 -
2025-07-11 17:42:39,232 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-11 17:42:39,557 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:39] "GET /socket.io/?EIO=4&transport=polling&t=PVvPkF6 HTTP/1.1" 200 -
2025-07-11 17:42:39,818 - __main__ - INFO - Client connected
2025-07-11 17:42:39,818 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:39] "POST /socket.io/?EIO=4&transport=polling&t=PVvPkK8&sid=dJXe14joEAliw7l4AAAE HTTP/1.1" 200 -
2025-07-11 17:42:39,866 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:39] "GET /socket.io/?EIO=4&transport=polling&t=PVvPkKA&sid=dJXe14joEAliw7l4AAAE HTTP/1.1" 200 -
2025-07-11 17:42:39,871 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:39] "GET /socket.io/?EIO=4&transport=polling&t=PVvPkP4&sid=dJXe14joEAliw7l4AAAE HTTP/1.1" 200 -
2025-07-11 17:42:41,768 - __main__ - INFO - Client disconnected
2025-07-11 17:42:41,769 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:42:41] "GET /socket.io/?EIO=4&transport=websocket&sid=dJXe14joEAliw7l4AAAE HTTP/1.1" 200 -
2025-07-11 17:43:22,691 - __main__ - ERROR - Authentication error: 'NeoAPI' object has no attribute 'login'
2025-07-11 17:43:22,691 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:43:22] "POST /api/authenticate HTTP/1.1" 200 -
2025-07-11 17:43:34,116 - __main__ - ERROR - Authentication error: 'NeoAPI' object has no attribute 'login'
2025-07-11 17:43:34,117 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:43:34] "POST /api/authenticate HTTP/1.1" 200 -
2025-07-11 17:45:24,317 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\Softwares\\kotak-neo-api-main\\trading_bot\\test_neo_api.py', reloading
2025-07-11 17:45:24,321 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\Softwares\\kotak-neo-api-main\\trading_bot\\test_neo_api.py', reloading
2025-07-11 17:45:25,340 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-11 17:45:26,652 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-11 17:45:26,653 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-11 17:45:26,663 - werkzeug - WARNING -  * Debugger is active!
2025-07-11 17:45:26,666 - werkzeug - INFO -  * Debugger PIN: 338-597-418
2025-07-11 17:45:27,300 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:45:27] "GET /socket.io/?EIO=4&transport=polling&t=PVvQNC4 HTTP/1.1" 200 -
2025-07-11 17:45:27,545 - __main__ - INFO - Client connected
2025-07-11 17:45:27,546 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:45:27] "POST /socket.io/?EIO=4&transport=polling&t=PVvQNH7&sid=8103Uxq3s7fwJ7BZAAAA HTTP/1.1" 200 -
2025-07-11 17:45:27,622 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:45:27] "GET /socket.io/?EIO=4&transport=polling&t=PVvQNH8&sid=8103Uxq3s7fwJ7BZAAAA HTTP/1.1" 200 -
2025-07-11 17:45:27,629 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:45:27] "GET /socket.io/?EIO=4&transport=polling&t=PVvQNMB&sid=8103Uxq3s7fwJ7BZAAAA HTTP/1.1" 200 -
2025-07-11 17:46:36,188 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\Softwares\\kotak-neo-api-main\\trading_bot\\app.py', reloading
2025-07-11 17:46:36,190 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\Softwares\\kotak-neo-api-main\\trading_bot\\app.py', reloading
2025-07-11 17:46:37,082 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-11 17:46:38,437 - __main__ - INFO - Starting Professional Kotak Neo Trading Bot
2025-07-11 17:46:38,437 - __main__ - INFO - Access the application at: http://localhost:5000
2025-07-11 17:46:38,448 - werkzeug - WARNING -  * Debugger is active!
2025-07-11 17:46:38,450 - werkzeug - INFO -  * Debugger PIN: 338-597-418
2025-07-11 17:46:39,289 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:46:39] "GET /socket.io/?EIO=4&transport=polling&t=PVvQemx HTTP/1.1" 200 -
2025-07-11 17:46:39,537 - __main__ - INFO - Client connected
2025-07-11 17:46:39,539 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:46:39] "POST /socket.io/?EIO=4&transport=polling&t=PVvQery&sid=nYlHnKyeHNh3wL28AAAA HTTP/1.1" 200 -
2025-07-11 17:46:39,600 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:46:39] "GET /socket.io/?EIO=4&transport=polling&t=PVvQerz&sid=nYlHnKyeHNh3wL28AAAA HTTP/1.1" 200 -
2025-07-11 17:46:39,606 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:46:39] "GET /socket.io/?EIO=4&transport=polling&t=PVvQewq&sid=nYlHnKyeHNh3wL28AAAA HTTP/1.1" 200 -
