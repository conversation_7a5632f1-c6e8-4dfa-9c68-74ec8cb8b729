#!/usr/bin/env python3
"""
Test NeoAPI to debug the login method issue
"""

import sys
import os

# Add the neo_api_client to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

print("=" * 60)
print("🔍 DEBUGGING NEOAPI LOGIN METHOD ISSUE")
print("=" * 60)

try:
    print("📦 Testing neo_api_client import...")
    import neo_api_client
    print("✅ neo_api_client imported successfully")
    
    print("\n🔍 Testing NeoAPI class...")
    print("Available in neo_api_client:", dir(neo_api_client))
    
    print("\n🔍 Creating NeoAPI instance...")
    client = neo_api_client.NeoAPI(
        consumer_key="test_key",
        consumer_secret="test_secret", 
        environment="uat"
    )
    print("✅ NeoAPI instance created")
    
    print("\n🔍 Checking client object type...")
    print("Client type:", type(client))
    print("Client class:", client.__class__)
    print("Client module:", client.__class__.__module__)
    
    print("\n🔍 Checking available methods...")
    methods = [method for method in dir(client) if not method.startswith('_')]
    print("Available methods:", methods)
    
    print("\n🔍 Checking for login method...")
    has_login = hasattr(client, 'login')
    print("Has login method:", has_login)
    
    if has_login:
        print("✅ login method found!")
        print("login method type:", type(getattr(client, 'login')))
        print("login method callable:", callable(getattr(client, 'login')))
    else:
        print("❌ login method NOT found!")
        print("Looking for similar methods...")
        login_like = [m for m in methods if 'login' in m.lower()]
        print("Login-like methods:", login_like)
    
    print("\n🔍 Checking for session_2fa method...")
    has_session_2fa = hasattr(client, 'session_2fa')
    print("Has session_2fa method:", has_session_2fa)
    
    print("\n🔍 Testing method call (if login exists)...")
    if has_login:
        try:
            # Don't actually call it with real credentials, just test if it's callable
            login_method = getattr(client, 'login')
            print("✅ login method is accessible")
            print("login method signature:", login_method.__doc__ if hasattr(login_method, '__doc__') else "No docstring")
        except Exception as e:
            print("❌ Error accessing login method:", e)
    
    print("\n" + "=" * 60)
    print("✅ DEBUGGING COMPLETE")
    print("=" * 60)
    
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()
