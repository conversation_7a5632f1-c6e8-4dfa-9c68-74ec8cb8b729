#!/usr/bin/env python3
"""
Simple Flask app to test if Flask is working
"""

print("Starting simple Flask test...")

try:
    from flask import Flask
    print("✅ Flask imported successfully")
    
    app = Flask(__name__)
    
    @app.route('/')
    def index():
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>🚀 Trading Bot - Flask Test</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    margin: 40px; 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    text-align: center;
                }
                .container { 
                    background: rgba(255,255,255,0.1); 
                    padding: 40px; 
                    border-radius: 20px; 
                    max-width: 600px; 
                    margin: 0 auto;
                    backdrop-filter: blur(10px);
                }
                .status { 
                    background: rgba(76, 175, 80, 0.2); 
                    padding: 20px; 
                    border-radius: 10px; 
                    margin: 20px 0;
                    border: 2px solid #4caf50;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 Professional Kotak Neo Trading Bot</h1>
                <h2>Flask Test Server</h2>
                
                <div class="status">
                    <h3>✅ Flask is Working!</h3>
                    <p><strong>URL:</strong> http://localhost:5000</p>
                    <p><strong>Status:</strong> Ready for main application</p>
                </div>
                
                <h3>🔧 Next Steps:</h3>
                <ol style="text-align: left; max-width: 400px; margin: 0 auto;">
                    <li>✅ Flask is working correctly</li>
                    <li>🔄 Test SocketIO integration</li>
                    <li>🔄 Start main trading application</li>
                    <li>📱 Ready for Kotak Neo credentials</li>
                </ol>
                
                <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
                    <p>Professional Kotak Neo Trading Bot v1.0</p>
                    <p>Flask server operational!</p>
                </div>
            </div>
        </body>
        </html>
        '''
    
    @app.route('/test')
    def test():
        return {
            'status': 'success',
            'message': 'Flask is working perfectly!',
            'server': 'Professional Kotak Neo Trading Bot',
            'version': '1.0.0'
        }
    
    if __name__ == '__main__':
        print("=" * 50)
        print("🚀 FLASK TEST SERVER")
        print("=" * 50)
        print("✅ Starting Flask server...")
        print("🌐 URL: http://localhost:5000")
        print("=" * 50)
        
        app.run(
            debug=False, 
            host='0.0.0.0', 
            port=5000,
            threaded=True
        )
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("📦 Please install Flask:")
    print("   pip install Flask")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
